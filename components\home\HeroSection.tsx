
import React, { Suspense } from 'react';
import Billie3DModel from '../billie/Billie3DModelPlaceholder';
import Button from '../ui/Button';
import NeonButton from '../ui/NeonButton';
import { BILLIE_TAGLINE } from '../../constants';
import { Link } from 'react-router-dom';
import { SparklesIcon } from '../ui/Icons';

const HeroSection: React.FC = () => {
  return (
    <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
      <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
        <div className="text-center md:text-left">
          <h1 className="font-display text-4xl sm:text-5xl lg:text-6xl font-black text-billieHeading mb-6 leading-tight animate-glow">
            Meet <span className="text-billieGold animate-glow">Billie</span> The Bear
          </h1>
          <p className="text-xl sm:text-2xl text-billieAccent mb-8 font-semibold animate-neon-pulse">
            {BILLIE_TAGLINE}
          </p>
          <p className="text-neutral-300 mb-10 text-lg">
            Dive into the world of Web3 with your guide through the crypto cosmos. Billie's seen it all, from bear market despair to bull run euphoria. Ready to join the adventure?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
            <Link to="/nfts">
              <NeonButton variant="purple" size="lg" leftIcon={<SparklesIcon className="w-5 h-5" />}>
                Mint Billie NFT
              </NeonButton>
            </Link>
            <Link to="/story">
              <NeonButton variant="gold" size="lg">
                Billie's Story
              </NeonButton>
            </Link>
          </div>

          {/* Floating text effects */}
          <div className="mt-8 flex flex-wrap gap-4 justify-center md:justify-start">
            <span className="px-3 py-1 bg-neonPurple/20 border border-neonPurple rounded-full text-neonPurple text-sm font-cyber animate-neon-pulse">
              WEB3 NATIVE
            </span>
            <span className="px-3 py-1 bg-neonGold/20 border border-neonGold rounded-full text-neonGold text-sm font-cyber animate-glow">
              DEFI READY
            </span>
            <span className="px-3 py-1 bg-neonCyan/20 border border-neonCyan rounded-full text-neonCyan text-sm font-cyber animate-neon-pulse">
              NFT POWERED
            </span>
          </div>
        </div>
        <div className="mt-12 md:mt-0 relative">
          {/* Glowing backdrop */}
          <div className="absolute inset-0 bg-gradient-to-br from-neonPurple/10 via-transparent to-neonGold/10 rounded-full blur-3xl animate-pulse" />

          <Suspense fallback={
            <div className="w-full max-w-lg aspect-square mx-auto bg-gradient-to-br from-neonPurple/20 via-billieGold/20 to-neonRed/20 rounded-full flex items-center justify-center animate-float border-2 border-neonPurple animate-neon-pulse shadow-glow-purple">
              <div className="text-billieGold text-lg font-semibold animate-glow font-display">Loading Billie...</div>
            </div>
          }>
            <Billie3DModel />
          </Suspense>

          {/* Orbiting elements */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-1/4 right-0 w-4 h-4 bg-neonCyan rounded-full animate-spin-slow opacity-60" />
            <div className="absolute bottom-1/4 left-0 w-3 h-3 bg-neonGreen rounded-full animate-bounce-slow opacity-60" />
            <div className="absolute top-1/2 left-1/4 w-2 h-2 bg-neonRed rounded-full animate-particle-float opacity-60" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
    