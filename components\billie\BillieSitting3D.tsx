import React, { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';

// 3D Model Component for Sitting Billie
const BillieSittingModel: React.FC = () => {
  const { scene } = useGLTF('/billieSitting3d.glb');
  const modelRef = useRef<THREE.Group>(null);

  // Add subtle floating animation
  useFrame((state) => {
    if (modelRef.current) {
      modelRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.05;
      modelRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.1;
    }
  });

  return (
    <group ref={modelRef}>
      <primitive object={scene} scale={[3, 3, 3]} />
    </group>
  );
};

// Loading fallback component
const LoadingFallback: React.FC = () => (
  <div className="w-full h-full bg-gradient-to-br from-neonPurple/20 via-billieGold/20 to-neonCyan/20 rounded-full flex items-center justify-center animate-float border-2 border-neonPurple animate-neon-pulse shadow-glow-purple">
    <div className="text-billieGold text-sm font-semibold animate-glow font-display">Loading Billie...</div>
  </div>
);

const BillieSitting3D: React.FC = () => {
  return (
    <div className="relative w-40 h-40 md:w-48 md:h-48 mx-auto">
      {/* Neon rings around the model */}
      <div className="absolute inset-0 rounded-full border-4 border-neonPurple animate-neon-pulse shadow-glow-purple opacity-60" />
      <div className="absolute inset-2 rounded-full border-2 border-neonGold animate-glow opacity-40" />
      <div className="absolute inset-4 rounded-full border border-neonCyan animate-neon-border opacity-30" />
      
      {/* 3D Canvas */}
      <div className="w-full h-full relative z-10">
        <Canvas
          camera={{ position: [0, 0, 8], fov: 45 }}
          style={{ width: '100%', height: '100%', background: 'transparent' }}
        >
          <Suspense fallback={null}>
            {/* Enhanced Lighting with colored lights */}
            <ambientLight intensity={0.4} />
            <directionalLight position={[5, 5, 5]} intensity={1} color="#7C3AED" />
            <pointLight position={[-5, -5, 5]} intensity={0.8} color="#FBBF24" />
            <pointLight position={[3, -3, 3]} intensity={0.6} color="#06B6D4" />

            {/* Environment for reflections */}
            <Environment preset="studio" />

            {/* 3D Model */}
            <BillieSittingModel />

            {/* Controls with gentle rotation */}
            <OrbitControls
              enablePan={false}
              enableZoom={false}
              maxPolarAngle={Math.PI / 2}
              minPolarAngle={Math.PI / 3}
              autoRotate
              autoRotateSpeed={0.5}
            />
          </Suspense>
        </Canvas>
      </div>
      
      {/* Floating particles around the model */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 6 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1.5 h-1.5 bg-neonGold rounded-full animate-particle-float opacity-60"
            style={{
              top: `${15 + (i * 12)}%`,
              left: `${10 + (i * 15)}%`,
              animationDelay: `${i * 0.8}s`,
              animationDuration: `${5 + i}s`,
            }}
          />
        ))}
      </div>

      {/* Holographic shimmer effect */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-5 transition-opacity duration-500 transform -translate-x-full hover:translate-x-full hover:transition-transform hover:duration-1000 pointer-events-none" />
    </div>
  );
};

// Preload the GLB model
useGLTF.preload('/billieSitting3d.glb');

export default BillieSitting3D;
